import {
  Compo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  ViewChild,
  ElementRef,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  TrackByFunction,
} from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { ProductsService } from 'src/app/services/products.service';
import { ReviewService } from 'src/app/services/review.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HttpErrorResponse } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
import moment from 'moment';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { Observable, Subscription, timer, Subject } from 'rxjs';
import { debounceTime, finalize } from 'rxjs/operators';
import { UserService } from 'src/app/services/user.service';
import { UndoSnackbarComponent } from '../../_dialogs/undo-snackbar/undo-snackbar.component';
import { MatSelect } from '@angular/material/select';
import { SnackbarService } from '../../services/snackbar.service';
import { FormGroup, FormBuilder } from '@angular/forms';
import { UndoBulkSnackbarComponent } from 'src/app/_dialogs/undobulk-snackbar/undobulk-snackbar.component';

@Component({
  selector: 'app-products',
  templateUrl: './products.component.html',
  styleUrls: ['./products.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProductsComponent implements OnInit, OnDestroy {
  datePickerOptions: any[];
  currentDate: any;
  customEndDate: string;
  customStartDate: string;
  maxDate: Date;
  minDate: Date;
  pageNumber;
  selected = 'recent';
  start_date;
  end_date;
  page: number = 1;
  size: number = 100;
  tableDataLoading: boolean;
  tabSwitchLoading: boolean = false; // Separate loading state for tab switching
  totalItems;
  subscriptionId;
  batch_id;
  bucketFromUrl;
  productHeaderData;
  displayedColumns;
  dataSource;
  productTableData: any[];
  selectedBucketSlug = 'IN_PROGRESS';
  selectedIndex;
  bucket;
  searchedItems: string[] = [];
  getBatchProgressTimer: Subscription;
  selectedProducts = {};
  permissionsObject;
  tabIndexWithData;
  update_in_progress: boolean = false;
  edit_in_progress: boolean = false;
  globalSelectionName: string = ''; // Initialize explicitly with empty string
  loading: boolean = false;
  isLoadingForOptions: boolean = false;
  searchError: string | null = null;
  isBulkEditOpen: boolean = false;
  public moveToForm: FormGroup;
  public bulkMoveToForm: FormGroup;
  private dropdownStates: { [key: string]: boolean } = {};
  private cellSearchTerms: { [key: string]: string } = {};

  private selectedValues: { [key: string]: string } = {};

  currentOpenDropdown: { rowId: string; slug: string } | null = null;
  dropdownSearchTerms: { [key: string]: string } = {};
  dropdowns: { [rowId: string]: { [columnSlug: string]: boolean } } = {};
  attributeOptions: any[] = [];
  bulkAttributeOptions: any[] = [];

  tabs: any[] = [
    // { name: 'In Queue', value: 'IN_QUEUE', slug: 'in_queue' },
    {
      name: 'In Progress',
      value: 'IN_PROGRESS',
      slug: 'in_progress',
      description: 'All SKUs that are undergoing classification',
    },
    {
      name: 'Accepted',
      value: 'ACCEPTED',
      slug: 'accepted',
      description:
        "All SKUs classified by dataX with high confidence and don't need to be reviewed. You can still choose to make minor edits and add comments here. Possible actions: Leave as 'Accepted' / Move to 'Review' / Move to 'Rework'",
    },
    {
      name: 'Review',
      value: 'REVIEW',
      slug: 'review',
      description:
        "SKUs that have been classified, but dataX has less confidence in, and require your review. You can make minor edits and add comments here. Possible actions: Move to 'Accepted' / Move to 'Rework'",
    },
    {
      name: 'Rework',
      value: 'REWORK',
      slug: 'rework',
      description:
        "SKUs that need to be classified again. Post-classification, the SKUs appear in the 'Accepted' bucket.",
    },
    {
      name: 'Insufficient Data',
      value: 'INSUFFICIENT_DATA',
      slug: 'insufficient_data',
      description: 'SKUs that cannot be classified because of missing data',
    },
  ];

  filterList: [];
  bucketWiseCount;
  search;
  selectable = true;
  removable = true;
  addOnBlur = true;
  searchedMultipleVals: string[] = [];
  datePickerValue;
  productFilterList;
  productPageFilters;
  readonly separatorKeysCodes: number[] = [ENTER, COMMA];
  allTabData = {};
  currentSlug;
  searchTerm: string = '';
  selectedAll = false;
  selectedSKUList: any = [];
  private searchSubject = new Subject<{
    value: string;
    type: 'bulkinput' | 'rowinput';
  }>();

  // TrackBy functions for performance optimization
  trackByRowId: TrackByFunction<any> = (index: number, item: any) => item?.row_id || index;
  trackBySlug: TrackByFunction<any> = (index: number, item: any) => item?.slug || index;
  trackByValue: TrackByFunction<any> = (index: number, item: any) => item?.value || index;
  trackByIndex: TrackByFunction<any> = (index: number) => index;

  // Computed properties to reduce template calculations
  get hasProductTableData(): boolean {
    return this.productTableData && this.productTableData.length > 0;
  }

  get isTableVisible(): boolean {
    return this.hasProductTableData && !this.tableDataLoading;
  }

  get showNoDataMessage(): boolean {
    return !this.tableDataLoading && (!this.productTableData || this.productTableData.length === 0);
  }

  get showPaginator(): boolean {
    return this.hasProductTableData;
  }

  // Helper methods to reduce template calculations
  isRowSelected(rowId: string): boolean {
    return this.selectedSKUList.includes(rowId);
  }

  shouldShowActions(tabSlug: string): boolean {
    return this.permissionsObject && this.permissionsObject[tabSlug]?.length > 0;
  }

  shouldShowSelectionBox(): boolean {
    return this.selectedSKUList.length > 1 ||
           (this.productTableData?.length === 1 && this.selectedSKUList.length === 1);
  }

  isInProgressTab(): boolean {
    return this.tabs && this.tabs[this.selectedIndex]?.value !== 'IN_PROGRESS';
  }

  // Pre-process data for faster rendering
  private preprocessTableData(data: any[]): any[] {
    if (!data || !Array.isArray(data)) return [];

    return data.map(item => {
      // Pre-compute all display values to avoid template calculations
      const processedItem = { ...item };

      // Pre-format dates
      if (item.created_at) {
        processedItem._formattedDate = new Date(item.created_at).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      }

      // Pre-truncate long text
      if (item.product_description?.value) {
        processedItem._truncatedDescription = item.product_description.value.length > 80
          ? item.product_description.value.substring(0, 80) + '...'
          : item.product_description.value;
      }

      if (item.category?.value) {
        processedItem._truncatedCategory = item.category.value.length > 10
          ? item.category.value.substring(0, 10) + '...'
          : item.category.value;
      }

      // Pre-compute selection state - but don't rely on it for new data
      processedItem._isSelected = false; // Reset for new data

      return processedItem;
    });
  }
  constructor(
    private productService: ProductsService,
    private reviewService: ReviewService,
    private matSnackbar: MatSnackBar,
    private router: Router,
    private route: ActivatedRoute,
    private userService: UserService,
    private snackbarService: SnackbarService,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef
  ) {}

  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild('menu') bulkEditMenu: ElementRef;

  ngOnInit() {
    // add subscription id from local storage in url as param
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    this.batch_id = this.route.snapshot.queryParams['batch_id'];
    this.bucketFromUrl = this.route.snapshot.queryParams['bucket'];
    this.selected = 'recent';
    const currentYear = new Date().getFullYear();
    this.minDate = new Date(currentYear - 20, 0, 1);
    this.maxDate = new Date();
    this.start_date = '';
    this.end_date = '';
    this.currentDate = moment().format('YYYY-MM-DD');

    this.moveToForm = this.fb.group({
      moveTo: [''],
    });
    this.bulkMoveToForm = this.fb.group({
      moveTo: [''],
    });

    // date picker options
    this.datePickerOptions = [
      {
        display: 'Recent',
        value: 'recent',
        start_date: '',
      },
      {
        display: 'Last Week',
        value: 'last_week',
        start_date: moment().subtract(7, 'day').format('YYYY-MM-DD'),
      },
      {
        display: 'Last Month',
        value: 'last_month',
        start_date: moment()
          .subtract(1, 'month')
          .add(1, 'day')
          .format('YYYY-MM-DD'),
      },
      {
        display: 'Last Quarter',
        value: 'last_quarter',
        start_date: moment()
          .subtract(3, 'month')
          .add(1, 'day')
          .format('YYYY-MM-DD'),
      },
      {
        value: 'custom_range',
      },
    ];
    // see if url has bucket available
    if (this.bucketFromUrl) {
      this.bucket = this.bucketFromUrl.toUpperCase();
      this.selectedIndex = this.tabs.findIndex(
        (tab) => tab.value == this.bucket
      );
    } else {
      this.bucket = this.tabs[0].value;
    }
    if (
      this.batch_id &&
      this.batch_id != '' &&
      this.searchedItems.indexOf(this.batch_id.trim()) < 0
    ) {
      this.searchedItems.push(this.batch_id.trim());
    }
    this.getBucketWiseCount(
      this.subscriptionId,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
    this.getProductTable(
      this.subscriptionId,
      this.page,
      this.size,
      this.bucket,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
    this.getProductFilterList(this.subscriptionId);
    this.getProductTableHeaders(this.subscriptionId);
    const ti = timer(0, 30000);
    this.getBatchProgressTimer = ti.subscribe((t) => {
      this.getBatchProgress();
    });
    this.permissionsObject = this.userService.appPermissions.bucket_permissions;

    this.searchSubject.pipe(debounceTime(300)).subscribe(({ value, type }) => {
      this.performSearch(value, type);
    });
  }

  ngOnDestroy() {
    this.getBatchProgressTimer ? this.getBatchProgressTimer.unsubscribe() : '';
    document.removeEventListener('click', this.closeBulkEdit);
    this.clearSearchTerm();
    this.searchSubject.complete();
  }

  /**
   * filter the dropdown options based on query
   * @param e
   */

  filterMyOptions(e: Event, inputType: 'bulkinput' | 'rowinput'): void {
    const searchValue = (e.target as HTMLInputElement).value;
    // Preserve the spaces in the search value
    const normalizedValue = searchValue.replace(/\s+/g, ' ').trim();

    if (!normalizedValue.length) {
      if (inputType === 'bulkinput') {
        this.bulkAttributeOptions = [];
      } else {
        this.attributeOptions = [];
      }
      return;
    }

    this.searchSubject.next({ value: normalizedValue, type: inputType });
  }

  private performSearch(
    searchValue: string,
    inputType: 'bulkinput' | 'rowinput'
  ): void {
    this.isLoadingForOptions = true;
    this.searchError = null;
    const cacheKey = `${inputType}-${searchValue}`;
    if (this.searchCache[cacheKey]) {
      this.handleSearchResults(this.searchCache[cacheKey], inputType);
      return;
    }

    this.productService
      .attributeSearch(this.subscriptionId, searchValue)
      .pipe(finalize(() => (this.isLoadingForOptions = false)))
      .subscribe({
        next: (resp) => {
          this.searchCache[cacheKey] = resp;
          this.handleSearchResults(resp, inputType);
        },
        error: (err: HttpErrorResponse) => {
          this.searchError = err.error.detail;
          if (inputType === 'bulkinput') {
            this.bulkAttributeOptions = [];
          } else {
            this.attributeOptions = [];
          }
        },
      });
  }

  private searchCache: { [key: string]: any[] } = {};
  private readonly MAX_RESULTS = 100;

  // Add this helper method
  private handleSearchResults(
    results: any[],
    inputType: 'bulkinput' | 'rowinput'
  ): void {
    const limitedResults = results?.slice(0, this.MAX_RESULTS) || [];

    if (inputType === 'bulkinput') {
      this.bulkAttributeOptions = limitedResults;
    } else {
      this.attributeOptions = limitedResults;
    }
  }

  // Add this method to get selected value
  getSelectedValue(rowId: string, columnSlug: string): string {
    const key = `${rowId}-${columnSlug}`;
    return this.selectedValues[key] || '';
  }

  /**
   * update selection for attribute
   * @param slug
   * @param value
   * @param element
   */
  updateSelection = (slug, value, element, prevValue) => {
    let obj = {
      slug: slug,
      value: value,
    };
    element[slug].edit_in_progress = true;
    element[slug].value = value;
    const key = `${element.row_id}-${slug}`;
    this.selectedValues[key] = value;
    this.productService
      .categoryUpdate(this.subscriptionId, element.row_id, obj)
      .subscribe({
        next: (resp) => {
          element[slug].edit_in_progress = false;
          this.snackbarService.openSnackBar(`${resp.detail}`, 'OK');
          this.attributeOptions = [];
          this.dropdownStates[key] = false;
          element[slug].update_in_progress = false;
          element[slug].value = value;
          const index = this.productTableData.findIndex(
            (item) => item.row_id === element.row_id
          );
          if (index !== -1) {
            this.productTableData[index] = { ...element };
            this.dataSource = new MatTableDataSource(this.productTableData);
          }
        },
        error: (err: HttpErrorResponse) => {
          element[slug].edit_in_progress = false;
          element[slug].value = prevValue;
          this.selectedValues[key] = prevValue;
          this.snackbarService.openSnackBar(`${err.error.detail}`, 'OK');
        },
      });
  };

  updateBulkOption = (slug, value, tableData, selectedRows) => {
    let obj = {
      slug: 'category_name',
      value: value,
      row_id_list: selectedRows // Use passed in selected rows instead of all rows
    };

    this.update_in_progress = true;
    this.productService
      .categoryUpdateBulkOption(
        this.subscriptionId,
        this.selectedBucketSlug,
        this.batch_id,
        obj
      )
      .subscribe({
        next: (resp) => {
          this.edit_in_progress = false;
          this.searchTerm = '';
          this.isBulkEditOpen = false;
          this.snackbarService.openSnackBar(`${resp.detail}`, 'OK');
          this.productService
            .getProductList(
              this.subscriptionId,
              this.page,
              this.size,
              this.bucket,
              this.searchedItems,
              this.searchedMultipleVals,
              this.start_date,
              this.end_date
            )
            .subscribe({
              next: (resp) => {
                this.update_in_progress = false;
                this.productTableData = resp.result;

                // Update existing dataSource data instead of creating new instance
                if (!this.dataSource) {
                  this.dataSource = new MatTableDataSource(this.productTableData);
                } else {
                  this.dataSource.data = this.productTableData;
                }

                // Keep only previously selected rows
                this.selectedSKUList = selectedRows;
                this.selectedAll = this.selectedSKUList.length === this.productTableData.length;

                // Trigger change detection manually for OnPush strategy
                this.cdr.markForCheck();
              },
            });
        },
        error: (err: HttpErrorResponse) => {
          this.isBulkEditOpen = false;
          this.snackbarService.openSnackBar(`${err.error.detail}`, 'OK');
          // Only clear selections on error
          this.selectedAll = false;
          this.selectedSKUList = [];
          this.globalSelectionName = '';
        },
      });
  };

  /**
   * get options for filters
   * @param subscription_id
   */
  getProductFilterList = (subscription_id) => {
    this.reviewService.getReviewFilterList(subscription_id, false).subscribe({
      next: (resp) => {
        this.productFilterList = resp.result;
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.matSnackbar.open(`${HttpResponse.error.detail}`, 'OK', {
          duration: 3000,
        });
      },
    });
  };

  /**
   * get Date
   * @param range
   * @returns
   */
  getDataForDate = (range: string) => {
    // do nothing for custom range
    if (this.selected == 'custom_range') {
      return null;
    }
    (this.customStartDate = ''), (this.customEndDate = '');
    // for others
    let interval = this.datePickerOptions.filter((item) => {
      return item.value === range;
    })[0];
    this.start_date = interval['start_date'];
    if (this.selected == 'recent') {
      this.end_date = '';
    } else {
      this.end_date = this.currentDate;
    }
    this.page = 1;
    this.getBucketWiseCount(
      this.subscriptionId,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
    this.getProductTable(
      this.subscriptionId,
      this.page,
      this.size,
      this.bucket,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
  };

  ed;
  sd;
  /**
   * set date range
   * @param dateRangeStart
   * @param dateRangeEnd
   */
  dateRangeChange = (
    dateRangeStart: HTMLInputElement,
    dateRangeEnd: HTMLInputElement
  ) => {
    if (
      moment(dateRangeStart?.value, 'DD-MM-YYYY').isValid() &&
      moment(dateRangeEnd?.value, 'DD-MM-YYYY').isValid()
    ) {
      this.selected = 'custom_range';
      this.sd = moment(dateRangeStart?.value, 'DD-MM-YYYY').format(
        'YYYY-MM-DD'
      );
      this.ed = moment(dateRangeEnd?.value, 'DD-MM-YYYY').format('YYYY-MM-DD');
      this.page = 1;
      if (this.selected == 'custom_range') {
        this.start_date = this.sd;
        this.end_date = this.ed;
      }
    }
    this.getBucketWiseCount(
      this.subscriptionId,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
    this.getProductTable(
      this.subscriptionId,
      this.page,
      this.size,
      this.bucket,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
  };

  /**
   * get filters on selection
   */
  getProductSelection = () => {
    this.searchedMultipleVals = [];
    this.selectedProducts;
    for (const property in this.selectedProducts) {
      if (this.selectedProducts[property]?.length) {
        this.selectedProducts[property].forEach((element) => {
          this.searchedMultipleVals.push(element);
        });
      }
    }
    this.page = 1;
    this.getBucketWiseCount(
      this.subscriptionId,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
    this.getProductTable(
      this.subscriptionId,
      this.page,
      this.size,
      this.bucket,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
  };

  /**
   * Called when tabs changed
   * @param tabChangeEvent
   */
  bucketTabChange = (tabChangeEvent): void => {
    // Immediate UI feedback - update tab state instantly
    this.selectedBucketSlug = tabChangeEvent.tab.textLabel;
    this.bucket = tabChangeEvent.tab.textLabel.toUpperCase();
    this.selectedIndex = tabChangeEvent.index;

    // Clear selections immediately for instant feedback
    this.selectedSKUList = [];
    this.selectedAll = false;
    this.globalSelectionName = '';
    this.bulkMoveToForm.get('moveTo')?.setValue(null);
    this.page = 1;

    // Show tab switching loading state immediately
    this.tabSwitchLoading = true;
    this.tableDataLoading = false; // Clear table loading to show tab switch loading

    // Immediate UI update
    this.cdr.detectChanges();

    // Use setTimeout to defer heavy operations and prevent UI blocking
    setTimeout(() => {
      this.tabSwitchLoading = false;
      this.getProductTable(
        this.subscriptionId,
        this.page,
        this.size,
        this.bucket,
        this.searchedItems,
        this.searchedMultipleVals,
        this.start_date,
        this.end_date
      );
    }, 0);
  };

  /**
   * get bucket wise count
   * @param subs_id
   */
  getBucketWiseCount = (
    subs_id,
    searchedItems,
    filters,
    start_date,
    end_date
  ) => {
    this.productService
      .getBucketCount(subs_id, searchedItems, filters, start_date, end_date)
      .subscribe({
        next: (resp) => {
          this.bucketWiseCount = resp.result;
          // go to the first tab which has the data
          this.tabIndexWithData = this.tabs.findIndex(
            (tab) => this.bucketWiseCount[tab.slug] > 0
          );
          this.selectedIndex = this.tabIndexWithData;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  };

  /**
   * Adds search query to the list on enter
   * @param event
   */

  addQuery = (searchVal) => {
    if (searchVal?.trim()) {
      const searchValue = searchVal.replace(/\s+/g, ' '); // Replace multiple spaces with single space
      if (this.searchedItems.indexOf(searchValue) < 0) {
        this.searchedItems.push(searchValue);
        this.batch_id = searchValue;

        // Refresh headers to show checkboxes
        this.getProductTableHeaders(this.subscriptionId);

        this.getBatchProgress();
        this.getBucketWiseCount(
          this.subscriptionId,
          this.searchedItems,
          this.searchedMultipleVals,
          this.start_date,
          this.end_date
        );
        this.getProductTable(
          this.subscriptionId,
          this.page,
          this.size,
          this.bucket,
          this.searchedItems,
          this.searchedMultipleVals,
          this.start_date,
          this.end_date
        );
      }
    }
    this.search = '';
  };

  /**
   * Removes search query from filter list
   * @param item
   */
  removeQuery = (item) => {
    const index = this.searchedItems.indexOf(item.value.trim());
    this.searchedItems.splice(index, 1);
    this.getBatchProgress();

    // Clear selections when removing last filter
    if (this.searchedItems.length === 0) {
      this.selectedAll = false;
      this.selectedSKUList = [];
    }

    // Refresh headers to update column visibility
    this.getProductTableHeaders(this.subscriptionId);

    this.getBucketWiseCount(
      this.subscriptionId,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
    this.getProductTable(
      this.subscriptionId,
      this.page,
      this.size,
      this.bucket,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
  };

  /**
   * Get is_batch boolean and progress for each filter
   */
  getBatchProgress = () => {
    this.productService
      .getBatchProgress(this.subscriptionId, this.searchedItems)
      .subscribe({
        next: (resp) => {
          this.filterList = resp.result;
        },
        error: (err: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(`${err.error.detail}`, 'OK');
        },
      });
  };

  /**
   * get product table headers
   * @param subs_id
   */
  getProductTableHeaders = (subs_id) => {
    this.productService.getProductHeader(subs_id).subscribe({
      next: (resp) => {
        // Start with the base headers
        this.productHeaderData = resp;

        // Add selection column first if we have search items
        if (this.searchedItems?.length > 0) {
          this.productHeaderData.unshift({
            slug: 'selection',
            display_name: '',
          });
        }

        // Add the remaining special columns
        this.productHeaderData.push({
          slug: 'actions',
          display_name: 'Actions',
        });
        this.productHeaderData.push({
          slug: 'comments',
          display_name: 'Comments',
        });

        if (this.productHeaderData) {
          this.displayedColumns = this.productHeaderData.map(
            (column) => column.slug
          );
        }
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.matSnackbar.open(`${HttpResponse.error.detail}`, 'OK', {
          duration: 3000,
        });
      },
    });
  };

  /**
   * get product table rows
   * @param subs_id
   * @param page
   * @param size
   * @param bucket
   * @param search
   * @param start_date
   * @param end_date
   */
  getProductTable = (
    subs_id,
    page,
    size,
    bucket,
    search_query,
    filter,
    start_date,
    end_date
  ) => {
    // Set loading state
    this.tableDataLoading = true;
    this.tabSwitchLoading = false; // Clear tab switch loading

    // Don't clear data immediately to avoid flickering
    // this.productTableData = [];

    // Initialize dataSource only once if it doesn't exist
    if (!this.dataSource) {
      this.dataSource = new MatTableDataSource([]);
    }

    this.productService
      .getProductList(
        subs_id,
        page,
        size,
        bucket,
        search_query,
        filter,
        start_date,
        end_date
      )
      .subscribe({
        next: (resp) => {
          // Use requestAnimationFrame for immediate rendering
          requestAnimationFrame(() => {
            this.tableDataLoading = false;

            // Pre-process data for faster template rendering
            this.productTableData = this.preprocessTableData(resp.result);
            this.totalItems = resp.total_items;
            this.size = resp.page_size;
            this.page = resp.page;

            // Direct assignment for fastest update
            this.dataSource.data = this.productTableData;

            // Immediate change detection
            this.cdr.detectChanges();
          });
        },
        error: (HttpResponse: HttpErrorResponse) => {
          requestAnimationFrame(() => {
            this.tableDataLoading = false;
            this.snackbarService.openSnackBar(
              `${HttpResponse.error.detail}`,
              'OK'
            );
            this.cdr.detectChanges();
          });
        },
      });
  };

  /**
   * Get table data on pagination
   * @param identifier
   * @param tabName
   */
  onPaginateChange = (identifier) => {
    this.page = identifier.pageIndex + 1;
    this.size = identifier.pageSize;
    this.getProductTable(
      this.subscriptionId,
      this.page,
      this.size,
      this.bucket,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
  };

  /**
   * reset filter
   */
  resetFilters = () => {
    this.page = 1;
    this.size = 100;
    this.search = '';
    this.selected = 'recent';
    this.datePickerValue = 'recent';
    this.searchedItems = [];
    this.searchedMultipleVals = [];
    this.start_date = '';
    this.end_date = '';
    this.selectedProducts = {};
    this.selectedIndex = 0;
    this.customStartDate = '';
    this.customEndDate = '';
    this.getProductFilterList(this.subscriptionId);
    this.filterList = [];
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { sub: this.subscriptionId },
    });
    this.getProductTable(
      this.subscriptionId,
      this.page,
      this.size,
      this.bucket,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
    this.getBucketWiseCount(
      this.subscriptionId,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
    this.globalSelectionName = '';
  };

  /**
   * Takes the focus to concerned tab
   * @param bucket
   */
  selectConcernedTab = (bucket) => {
    // Immediate UI updates
    this.selectedSKUList = [];
    this.selectedAll = false;
    this.bucket = bucket.toUpperCase();
    let tabIndex = this.tabs.findIndex((tab) => tab.value == bucket);
    this.selectedIndex = tabIndex;
    this.page = 1;
    this.tableDataLoading = true;

    // Immediate UI feedback
    this.cdr.detectChanges();

    // Defer heavy operations to prevent UI blocking
    setTimeout(() => {
      // Refresh bucket counts and table data sequentially
      this.getBucketWiseCount(
        this.subscriptionId,
        this.searchedItems,
        this.searchedMultipleVals,
        this.start_date,
        this.end_date
      );

      // Load table data for the new bucket
      this.getProductTable(
        this.subscriptionId,
        this.page,
        this.size,
        this.bucket,
        this.searchedItems,
        this.searchedMultipleVals,
        this.start_date,
        this.end_date
      );
    }, 0);
  };

  selectAllItems(e: any) {
    if (e.checked || e == 'all') {
      this.selectedAll = true;
      // Ensure we get all unique row_ids from current table data
      this.selectedSKUList = [...new Set(this.productTableData.map((obj) => obj.row_id))];
    } else {
      this.selectedAll = false;
      this.selectedSKUList = [];
    }
    // Clear globalSelectionName whenever selection changes
    this.globalSelectionName = '';

    // Trigger change detection to update UI
    this.cdr.markForCheck();
  }

  selectSKU(e: any, skuDetails: any) {
    if (e.checked) {
      // Only add if not already selected (prevent duplicates)
      if (!this.selectedSKUList.includes(skuDetails.row_id)) {
        this.selectedSKUList.push(skuDetails.row_id);
      }
    } else {
      const index = this.selectedSKUList.findIndex(
        (node: any) => node === skuDetails.row_id
      );
      if (index > -1) {
        this.selectedSKUList.splice(index, 1);
      }
    }

    // Update selectedAll state based on actual counts
    this.selectedAll = this.selectedSKUList.length === this.productTableData.length && this.productTableData.length > 0;

    // Clear globalSelectionName when selection changes
    if (this.selectedSKUList.length < 2) {
      this.globalSelectionName = '';
    }

    // Trigger change detection to update UI
    this.cdr.markForCheck();
  }

  isRowIdSelected(rowId: any): boolean {
    return this.selectedSKUList.includes(rowId);
  }

  /**
   * To update current bucket to new bucket
   * @param rowId
   * @param bucketValue
   */

  updateBucket = (rowId, currentBucket, newBucket) => {
    const retrieveBucket = currentBucket;
    let obj = { bucket: newBucket };
    this.productService
      .bucketUpdate(rowId, obj, this.subscriptionId)
      .subscribe({
        next: (resp) => {
          this.selectConcernedTab(newBucket);
          this.moveToForm.controls['moveTo'].setValue('');

          this.matSnackbar.openFromComponent(UndoSnackbarComponent, {
            duration: 3000,
            data: {
              response: resp.detail,
              subscriptionId: this.subscriptionId,
              id: rowId,
              onUndo: () => {
                this.selectConcernedTab(retrieveBucket);
              },
            },
          });
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.moveToForm.controls['moveTo'].setValue('');
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  };

  previousSelectedSKUList: any;
  bulkUpdateBucket = (tableData, currentBucket, newBucket) => {
    const previousBucket = currentBucket;
    this.previousSelectedSKUList = [...this.selectedSKUList];

    let obj = {
      move_to_bucket: newBucket,
      row_id_list: this.selectedSKUList,
    };

    this.productService
      .bnulkBucketUpdate(
        obj,
        this.batch_id,
        this.selectedBucketSlug,
        this.subscriptionId
      )
      .subscribe({
        next: (resp) => {
          this.selectConcernedTab(newBucket);
          this.moveToForm.controls['moveTo'].setValue('');
          const snackBarRef = this.matSnackbar.openFromComponent(
            UndoBulkSnackbarComponent,
            {
              duration: 4000,
              data: {
                response: resp.detail,
                subscriptionId: this.subscriptionId,
                row_id_list: this.previousSelectedSKUList,
                previousBucket: previousBucket,
                onUndo: () => {
                  this.selectConcernedTab(previousBucket);
                },
              },
            }
          );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.moveToForm.controls['moveTo'].setValue('');
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  };

  bulkUpdateOptions = (tableData, newBucket) => {
    const retrieveBucket = this.selectedBucketSlug;
    let obj = {};
    if (this.selectedSKUList.length == tableData.length) {
      obj = {
        move_to_bucket: newBucket,
        row_id_list: this.selectedSKUList,
      };
    } else {
      obj = {
        move_to_bucket: newBucket,
        row_id_list: this.selectedSKUList,
      };
    }
    this.productService
      .bnulkBucketUpdate(
        obj,
        this.batch_id,
        this.selectedBucketSlug,
        this.subscriptionId
      )
      .subscribe({
        next: (resp) => {
          this.selectConcernedTab(newBucket);
          this.moveToForm.controls['moveTo'].setValue('');
          const snackBarRef = this.matSnackbar.open(resp.detail, 'Ok');
          snackBarRef.afterDismissed().subscribe((info) => {});
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.moveToForm.controls['moveTo'].setValue('');
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  };

  clearSearchTerm() {
    this.bulkAttributeOptions = [];
    this.searchTerm = '';
    this.cellSearchTerms = {};
    this.searchCache = {};
  }

  //-----------toggle dropdown section----------------

  toggleDropdown(rowId: string, slug: string, element: any, event: Event) {
    event.stopPropagation();
    if (this.isBulkEditOpen) {
      return;
    }

    if (
      this.currentOpenDropdown?.rowId === rowId &&
      this.currentOpenDropdown?.slug === slug
    ) {
      this.currentOpenDropdown = null;
      this.dropdownSearchTerms = {};
      this.attributeOptions = [];
      return;
    }

    if (this.currentOpenDropdown) {
      this.dropdownSearchTerms = {};
      this.attributeOptions = [];
    }
    this.currentOpenDropdown = { rowId, slug };
  }

  handleClickOutside(rowId: string, slug: string, element: any) {
    if (
      this.currentOpenDropdown?.rowId === rowId &&
      this.currentOpenDropdown?.slug === slug
    ) {
      this.currentOpenDropdown = null;
      this.dropdownSearchTerms = {};
      this.attributeOptions = [];
    }
    this.isBulkEditOpen = false;
  }

  isDropdownOpen(rowId: string, slug: string): boolean {
    return (
      this.currentOpenDropdown?.rowId === rowId &&
      this.currentOpenDropdown?.slug === slug
    );
  }

  getSearchTermForCell(rowId: string, slug: string): string {
    const key = `${rowId}-${slug}`;
    return this.dropdownSearchTerms[key] || '';
  }

  updateSearchTerm(term: string, rowId: string, slug: string) {
    const key = `${rowId}-${slug}`;
    this.dropdownSearchTerms[key] = term;
  }

  toggleBulkEdit(menuElement: any) {
    if (this.currentOpenDropdown) {
      return;
    }
    this.isBulkEditOpen = !this.isBulkEditOpen;

    if (this.isBulkEditOpen) {
      setTimeout(() => {
        document.addEventListener('click', this.closeBulkEdit);
      });
    } else {
      this.clearSearchTerm();
    }
  }

  private closeBulkEdit = (event: any) => {
    if (
      this.bulkEditMenu &&
      !this.bulkEditMenu.nativeElement.contains(event.target)
    ) {
      this.isBulkEditOpen = false;
      document.removeEventListener('click', this.closeBulkEdit);
      this.clearSearchTerm();
    }
  };

  toggleBulkDropdown(): void {
    if (this.currentOpenDropdown) {
      return;
    }
    this.isBulkEditOpen = !this.isBulkEditOpen;
    if (!this.isBulkEditOpen) {
      this.clearSearchTerm();
      this.bulkAttributeOptions = [];
    }
  }

  handleBulkClickOutside(): void {
    this.isBulkEditOpen = false;
    this.clearSearchTerm();
    this.bulkAttributeOptions = [];
  }

  handleBulkSelection(option: string): void {
    this.globalSelectionName = option;

    // Don't modify existing selections
    // Keep only currently selected rows
    const currentlySelectedRows = [...this.selectedSKUList];

    this.updateBulkOption('category_name', option, this.productTableData, currentlySelectedRows);
    this.isBulkEditOpen = false;
    this.clearSearchTerm();
  }
}

