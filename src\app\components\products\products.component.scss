@import "../../../styles/variables";

// Performance optimizations for table rendering
::ng-deep .mat-table {
  will-change: transform;
  contain: layout style paint;
  transform: translateZ(0); // Force hardware acceleration
}

::ng-deep .mat-row {
  will-change: transform;
  contain: layout style;
}

::ng-deep .mat-cell {
  contain: layout style;
}

// Optimize table viewport for large datasets
.table-viewport {
  height: 600px;
  contain: strict;
}

::ng-deep .mat-checkbox .mat-checkbox-frame {
  border-radius: 4px;
}

::ng-deep .mat-checkbox .mat-checkbox-background {
  border-radius: 4px;
}
.product-filter {
  width: auto;
  height: 40px;
  border: 1px solid #c1c4d6;
  box-sizing: border-box;
  border-radius: 4px;

  .mat-select {
    width: 92%;
    padding-left: 10px;
    position: absolute;
    margin-top: -10px;
    .mat-select-placeholder {
      color: #3b3e48;
    }
    .mat-select-arrow {
      color: #3b3e48;
    }
  }
}
.batch-chip {
  margin-top: 5px;
  margin-bottom: -5px;
  font-family: $site-font;
  font-style: normal;
  font-weight: normal;
  font-size: 10px;
  line-height: 15px;
  width: fit-content;
  padding-left: 5px;
  padding-right: 5px;
  color: $theme-white;
  box-sizing: border-box;
  border-radius: 40px;
}
.search-chip {
  margin: 15px 20px;
}
.exit-edit {
  width: 18px;
  height: 18px;
  font-size: 18px;
  cursor: pointer;
  color: #797c7e;
}
.disable-exit {
  pointer-events: none;
  cursor: none;
}

.view-messages-icon {
  color: #3b3e48;
  cursor: pointer;
  display: flex;
  height: 40px;
  align-items: center;
}
.edit-btn {
  width: 18px;
  height: 18px;
  font-size: 18px;
  padding-left: 8px;
  cursor: pointer;
  color: #797c7e;
}
.edit-btn-row {
  width: 18px;
  height: 18px;
  padding: 0px 0px 4px 4px;
  font-size: 18px;
  cursor: pointer;
  color: #797c7e;
}
.comments-icon {
  cursor: pointer;
  .dot-comments {
    position: relative;
    height: 8px;
    width: 8px;
    border-radius: 50%;
    background-color: $theme-red;
    top: 5px;
    left: 16px;
  }
}
.selectionBoxContainer{
  padding: 0px 0px 16px 0px;
  background-color: #F4F6FA;
    .brandHeader{
      font-weight: 600;
    }


}
.selectionBox {
  height: 56px;
  padding: 0px 20px;
  background-color: #EDEFF5;
  box-shadow: 0px 2px 4px -2px #0000000D;
  border-radius: 4px;

}

.separator {
  border: 1px solid #D0D3DD;
  width: 0px;
  height: 20px;
  margin: 0px 20px;
}


.text-container {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}

.action-text {
  text-decoration: underline;
  font-weight: 500;
  cursor: pointer;
  color: #43683E;
  font-size: 16px;
}
.skuLengthCount{
  font-weight: 500;
  font-size: 16px;
}

.attribute-search-dropdown {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.leafNodeEditName{
  font-weight: 400;
  font-size: 14px;
}
.move_to_frmField{
  width: 120px;
}

.leaf-node-container {
  display: contents;
  align-items: center;
  column-gap: 8px;
  .leaf-node-column {
   margin-left: 10px;
   cursor: pointer;
   transition: color 0.2s;
  }
}

.search-result-item:hover {
  background-color: #f1f1f1;
}
.loading-option {
  height: 48px !important;
  line-height: 48px !important;
}

.loading-container {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  mat-progress-spinner {
    margin-right: 12px;
  }
}
.custom-menu-height {
  max-height: 300px;
  overflow-y: auto;
}

.leafnodeContaner {
  position: relative;

  .menu-container {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    min-width: 200px;
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    margin-top: 4px;
  }


  .view-mode {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
  }

  .edit-btn-row {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(0,0,0,0.04);
    }
  }
}

.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}


.dropdown-content-bulk{
   margin: 14px -45px;
   position: absolute;
   background: white;
   border-radius: 8px;
   box-shadow: 0 2px 4px rgba(0,0,0,0.2);
   width: 250px;
   overflow: hidden;
   max-height: 400px;
   display: flex;
   flex-direction: column;
   z-index: 1000;
   .search-header {
    position: sticky;
    top: 0;
    background: white;
    z-index: 1;
    border-bottom: 1px solid rgba(0, 0, 0, 0.42);
  }

  input {
    padding: 8px;
    width: 100%;
    font-family: inherit;
    font-size: inherit;
    line-height: 1.5;
    border: none;
    background: transparent;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    font-feature-settings: "kern";

    &::placeholder {
      white-space: normal;
    }

    &:focus {
      outline: none;
    }
  }
}

.dropdown-content {
  position: absolute;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  width: 250px;
  overflow: hidden;
  max-height: 400px;
  display: flex;
  flex-direction: column;
  z-index: 1000;

  .search-header {
    position: sticky;
    top: 0;
    background: white;
    z-index: 1;
    border-bottom: 1px solid rgba(0, 0, 0, 0.42);
  }

  input {
    padding: 8px;
    width: 100%;
    font-family: inherit;
    font-size: inherit;
    line-height: 1.5;
    border: none;
    background: transparent;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    font-feature-settings: "kern";

    &::placeholder {
      white-space: normal;
    }

    &:focus {
      outline: none;
    }
  }
}

.options-container {
  max-height: 300px;
  overflow: hidden;
  position: relative;

  .scrollable-results {
    max-height: 250px;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;

    /* Custom scrollbar styles */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  }
}

.search-result-item {
  height: 36px;
  font-weight: 400;
  font-size: 14px;
  padding: 0 16px;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.selection-name {
  display: inline-block;
  padding: 4px;
  color: #333;
  font-weight: 500;

  &:empty:before {
    content: 'No selection';
    color: #999;
    font-style: italic;
  }
}

.search-filter {
  input {
    padding: 0 8px;
    width: 100%;
    font-family: inherit;
    font-size: inherit;
    &::placeholder {
      white-space: pre;
    }
  }

  .mat-option {
    height: auto;
    line-height: normal;
    padding: 8px;
  }

  .mat-form-field-wrapper {
    padding-bottom: 0;
  }

  .search-icon {
    margin: 0 8px;
  }
}

.search-input-container {
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-option {
  display: flex;
  align-items: center;
  padding: 2px 12px;
  background: white;
  cursor: pointer;
  transition: background 0.3s ease-in-out;
  border-radius: 4px;
}

.search-option:hover {
  background: rgba(0, 0, 0, 0.04);
}

.search-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.search-icon {
  color: rgba(0, 0, 0, 0.5);
  margin-right: 8px;
  &:hover {
    color: #3f51b5;
  }
}


.search-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  padding: 4px 0;
}




